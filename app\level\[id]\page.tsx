'use client'

import { useSession } from 'next-auth/react'
import { log } from '@/app/lib/logger';
import { useEffect, useState, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'
import { useNavbar } from '../../components/NavbarContext'

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
    attempts: number
  }>
}

interface MainTask {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  hasAccess: boolean
  isLocked: boolean
  buttonText?: string
  requiredScore?: number
  children: Level[]
}

export default function LevelPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const { setNavbarData } = useNavbar()
  const levelId = params.id as string

  const [mainTask, setMainTask] = useState<MainTask | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const fetchMainTask = useCallback(async () => {
    try {
      const response = await fetch('/api/levels')
      if (response.ok) {
        const mainTasks = await response.json()
        const currentMainTask = mainTasks.find((task: MainTask) => task.id === levelId)
        if (currentMainTask) {
          setMainTask(currentMainTask)
          // 设置导航栏数据
          const completedCount = currentMainTask.children.filter((level: Level) =>
            level.progress && level.progress.length > 0 && level.progress[0].completed
          ).length
          setNavbarData({
            completedCount,
            totalCount: currentMainTask.children.length
          })
        } else {
          router.push('/dashboard')
        }
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      log.error('获取主任务失败:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [levelId, router, setNavbarData])

  useEffect(() => {
    if (session && levelId) {
      fetchMainTask()
    }
  }, [session, levelId, fetchMainTask])

  const calculateProgress = (level: Level) => {
    if (!level.progress || level.progress.length === 0) return 0
    return level.progress[0].completed ? 100 : 0
  }

  const getCompletedCount = () => {
    if (!mainTask) return 0
    return mainTask.children.filter(level =>
      level.progress && level.progress.length > 0 && level.progress[0].completed
    ).length
  }

  const getEarnedPoints = () => {
    if (!mainTask) return 0
    return mainTask.children
      .filter(level => level.progress && level.progress.length > 0 && level.progress[0].completed)
      .reduce((sum, level) => sum + level.points, 0)
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session || !mainTask) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style={{ paddingTop: '6rem' }}>
      <div className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        {/* 主任务信息 */}
        <div className="bg-white shadow-xl rounded-2xl p-8 mb-8 border border-gray-100">
          {/* 头部区域 */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white text-2xl">🗺️</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                  {mainTask.name}
                </h1>
                <p className="text-gray-600 text-lg leading-relaxed max-w-2xl">
                  {mainTask.description}
                </p>
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* 难度等级 */}
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">难度等级</span>
                <span className="text-yellow-600">⭐</span>
              </div>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <span
                    key={i}
                    className={`text-lg ${
                      i < mainTask.difficulty ? 'text-yellow-500' : 'text-gray-300'
                    }`}
                  >
                    ★
                  </span>
                ))}
              </div>
            </div>

            {/* 总经验值 */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">总经验值</span>
                <span className="text-blue-600">💎</span>
              </div>
              <div className="text-2xl font-bold text-blue-700">{mainTask.points}</div>
            </div>

            {/* 已获经验值 */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">已获经验值</span>
                <span className="text-green-600">🏆</span>
              </div>
              <div className="text-2xl font-bold text-green-700">{getEarnedPoints()}</div>
            </div>

            {/* 关卡进度 */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">关卡进度</span>
                <span className="text-purple-600">🎯</span>
              </div>
              <div className="text-2xl font-bold text-purple-700">
                {getCompletedCount()}/{mainTask.children.length}
              </div>
            </div>
          </div>
        </div>

        {/* 子任务列表 */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            🧩 关卡列表
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mainTask.children.map((level) => {
            const progress = calculateProgress(level)
            const isCompleted = progress === 100
            const userProgress = level.progress && level.progress.length > 0 ? level.progress[0] : null

            // 检查是否是受限关卡
            const isAdvancedLevel = mainTask.name === '进阶操作' || mainTask.name === '实用技巧'
            const isLocked = isAdvancedLevel && mainTask.isLocked

            return (
              <div
                key={level.id}
                className={`bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 flex flex-col h-full relative ${
                  isCompleted
                    ? 'ring-2 ring-green-200 bg-gradient-to-br from-green-50 to-white'
                    : isLocked
                    ? 'opacity-75 cursor-not-allowed'
                    : 'hover:border-blue-200'
                }`}
              >
                {/* 锁定/解锁图标 */}
                {isAdvancedLevel && (
                  <div className="absolute top-4 right-4 z-10">
                    {isLocked ? (
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>
                )}

                {/* 卡片顶部装饰条 */}
                <div className={`h-1 rounded-full mb-6 ${
                  isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                  isLocked ? 'bg-gradient-to-r from-red-400 to-red-500' :
                  'bg-gradient-to-r from-blue-500 to-indigo-500'
                }`}></div>

                {/* 头部区域 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center shadow-lg ${
                      isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-blue-500 to-indigo-500'
                    }`}>
                      <span className="text-white text-xl">
                        {isCompleted ? '✅' : '🧩'}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 mb-1">
                        {level.name}
                      </h3>
                      {/* 为已完成状态预留空间，保持高度一致 */}
                      <div className="h-6 flex items-center">
                        {isCompleted && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            已完成
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex-1">
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {level.description}
                  </p>

                  {/* 统计信息网格 */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 rounded-xl p-3 text-center">
                      <div className="flex items-center justify-center space-x-1 mb-1">
                        {[...Array(5)].map((_, i) => (
                          <span
                            key={i}
                            className={`text-sm ${
                              i < level.difficulty ? 'text-yellow-500' : 'text-gray-300'
                            }`}
                          >
                            ★
                          </span>
                        ))}
                      </div>
                      <div className="text-xs text-gray-500">难度等级</div>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-3 text-center">
                      <div className="text-lg font-bold text-blue-600">{level.points}</div>
                      <div className="text-xs text-gray-500">经验值</div>
                    </div>
                  </div>

                  {/* 进度条 */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">学习进度</span>
                      <span className="text-sm font-bold text-gray-900">{progress}%</span>
                    </div>
                    <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div
                        className={`h-3 rounded-full transition-all duration-500 ${
                          isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-blue-500 to-indigo-500'
                        }`}
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                    {userProgress && (
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500">
                          已获得 <span className="font-medium text-green-600">{userProgress.score}</span> 经验值
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 行动按钮 - 固定在底部 */}
                <div className="mt-auto">
                  {isLocked ? (
                    <div className="block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold bg-gray-300 text-gray-500 cursor-not-allowed">
                      <span className="flex items-center justify-center space-x-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>需要邀请码解锁</span>
                      </span>
                    </div>
                  ) : (
                    <Link
                      href={`/task/${level.tasks[0]?.id || level.id}`}
                      className={`block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                        isCompleted
                          ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600'
                          : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600'
                      }`}
                    >
                      <span className="flex items-center justify-center space-x-2">
                        <span>{isCompleted ? '🔄 再次挑战' : '🚀 开始挑战'}</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                    </Link>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}